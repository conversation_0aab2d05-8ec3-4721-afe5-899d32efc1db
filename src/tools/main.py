import os
import dotenv

dotenv.load_dotenv()

from typing import Literal
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.state import CompiledStateGraph
from langgraph.checkpoint.memory import MemorySaver
from tools import tools, tool_node
from state import AgentState
from models import gpt_4o_mini
from utils import visualize_graph

from lmnr import Laminar

Laminar.initialize(project_api_key=os.getenv("LMNR_API_KEY"))


def should_continue(state: AgentState) -> Literal["tools", "end"]:
    """Determine whether to continue with tools or end the conversation."""
    last_message = state["messages"][-1]
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        return "tools"
    return "end"


def call_model(state: AgentState):
    """Call the model to generate a response."""
    messages = state["messages"]
    model = gpt_4o_mini.bind_tools(tools)
    response = model.invoke(messages)
    return {"messages": [response]}


# Create the graph
def create_agent_graph() -> CompiledStateGraph:
    """Create and compile the LangGraph agent."""
    workflow = StateGraph(AgentState)

    # Add nodes
    workflow.add_node("agent", call_model)
    workflow.add_node("tools", tool_node)

    # Set the entrypoint
    workflow.add_edge(START, "agent")

    # Add conditional edges
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "tools": "tools",
            "end": END,
        },
    )

    # Add edge from tools back to agent
    workflow.add_edge("tools", "agent")

    # Add memory
    memory = MemorySaver()

    # Compile the graph
    app = workflow.compile(checkpointer=memory)

    return app


def run_example_conversation(app: CompiledStateGraph):
    """Run an example conversation with the agent."""
    config = {"configurable": {"thread_id": "example_conversation"}}

    print("\n🤖 Starting example conversation...")
    print("=" * 60)

    # Example queries to test different tools
    test_queries = [
        "What's the weather like in London?",
        "Calculate 25 * 4 + 10",
        "Search for information about artificial intelligence",
        "Add a new task: 'Prepare presentation for Monday'",
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: {query}")
        print("-" * 40)

        # Create the input
        inputs = {"messages": [HumanMessage(content=query)]}

        # Run the agent
        try:
            result = app.invoke(inputs, config)

            # Print the response
            for message in result["messages"]:
                if isinstance(message, AIMessage):
                    if hasattr(message, "tool_calls") and message.tool_calls:
                        print(f"🔧 Agent is calling tools...")
                        for tool_call in message.tool_calls:
                            print(f"   - {tool_call['name']}: {tool_call['args']}")
                    else:
                        print(f"🤖 Agent: {message.content}")
                elif isinstance(message, ToolMessage):
                    print(f"🛠️  Tool Result: {message.content}")

        except Exception as e:
            print(f"❌ Error: {e}")

        print()


def main():
    """Main function to demonstrate the LangGraph application."""
    print("🚀 Setting up LangGraph Application with Tools")
    print("=" * 60)

    # Create the agent
    app = create_agent_graph()

    # Generate visualization
    print("\n📊 Generating Graph Visualization...")
    if visualize_graph(app):
        print("✅ Graph structure created successfully!")

    # Show graph info
    print(f"\n📋 Graph Information:")
    print(f"   - Nodes: {list(app.get_graph().nodes.keys())}")
    print(f"   - Available Tools: {[tool.name for tool in tools]}")

    # Run example conversation
    run_example_conversation(app)

    print("\n🎉 LangGraph application setup complete!")
    print("\nThe graph includes:")
    print("• Agent node: Uses ChatOpenAI to process queries")
    print("• Tools node: Executes tool calls")
    print("• Conditional routing: Decides whether to use tools or end")
    print("• Memory: Maintains conversation state")
    print("• Available tools: search, calculator, weather, task_manager")


if __name__ == "__main__":
    main()
